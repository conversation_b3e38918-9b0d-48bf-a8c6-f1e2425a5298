# PhotoViewer2

A modern image viewer application built with C# .NET 6 and Windows Forms.

## Features

- **Image Format Support**: PNG, JPG, JPEG, GIF, WebP, BMP
- **Alpha Channel Support**: Proper transparency display with checkered background
- **Navigation**: Previous/Next image browsing within folders
- **Zoom Controls**: Zoom in, zoom out, fit to window
- **Rotate Function**: Rotate images 90 degrees clockwise
- **Crop Functionality**: Select and crop image areas with mouse
- **Save Options**: Save images in various formats
- **Drag & Drop**: Drop image files to open them
- **Keyboard Shortcuts**: Arrow keys for navigation, +/- for zoom, R for rotate, Ctrl+O to open, Ctrl+S to save

## Requirements

- .NET 6.0 Runtime (Windows)
- Windows operating system

## Dependencies

- System.Drawing.Common 7.0.0 - Image processing
- Svg 3.4.7 - SVG icon rendering

## Building

```bash
dotnet build
```

## Running

```bash
dotnet run
```

Or run the executable from `bin/Debug/net6.0-windows/PhotoViewer2.exe`

## File Associations

To set up file associations so you can double-click image files to open them with PhotoViewer2:

1. Run the provided batch file as administrator:
   ```cmd
   setup-file-associations.bat
   ```

2. Or manually set up associations:
   - Right-click on an image file
   - Select "Open with" → "Choose another app"
   - Browse to `bin/Debug/net6.0-windows/PhotoViewer2.exe`
   - Check "Always use this app to open .png files" (or the respective format)

## Usage

### Opening Images
- Click the "Open" button in the toolbar
- Drag and drop image files onto the application
- Use Ctrl+O keyboard shortcut
- Double-click image files (after setting up file associations)
- Command line: `PhotoViewer2.exe "path\to\image.jpg"`

### Navigation
- Use Previous/Next buttons in toolbar
- Use Left/Right arrow keys
- Automatically loads all images from the same folder

### Zoom Controls
- Zoom In/Out buttons in toolbar
- +/- keys on keyboard
- "Fit to Window" button to auto-fit image

### Rotate Feature
- Click the "Rotate" button in toolbar to rotate 90 degrees clockwise
- Use 'R' key for quick rotation
- Automatically adjusts zoom to fit rotated image

### Crop Feature
1. Click the "Crop" button to enter crop mode
2. Click and drag to select the area to crop
3. Confirm the crop operation
4. Press Escape to exit crop mode

### Saving
- Click "Save As" button in toolbar
- Use Ctrl+S keyboard shortcut
- Choose from PNG, JPEG, GIF, or BMP formats

## Icons

The application uses SVG icons embedded in a separate `PhotoViewer.Icons.dll` library for crisp display at any size. The following icons are included:
- OpenFolder.svg - Open folder icon
- Previous.svg / Next.svg - Navigation icons
- ZoomIn.svg / ZoomOut.svg - Zoom control icons
- Rotate90Clockwise.svg - Rotate icon
- Crop.svg - Crop icon
- Save.svg - Save icon

### Icons Library Benefits:
- **Embedded Resources**: All SVG icons are embedded in PhotoViewer.Icons.dll
- **Scalable Vector Graphics**: Crisp display at any DPI setting
- **Small File Sizes**: Efficient SVG format
- **Easy Distribution**: Single DLL contains all icons
- **Professional Appearance**: High-quality vector graphics

## Architecture

### Main Application
- **MainForm.cs**: Main application window with all UI controls and event handlers
- **TransparentPictureBox**: Custom PictureBox with transparency support
- **Program.cs**: Application entry point with command-line argument support
- **PhotoViewer.ico**: Application icon for executable and window

### Icons Library (PhotoViewer.Icons.dll)
- **IconProvider**: Static class providing access to embedded SVG icons
- **Embedded SVG Resources**: All icons stored as embedded resources
- **High-Quality Rendering**: SVG to bitmap conversion with anti-aliasing

### Technology Stack
- Built with Windows Forms for native Windows experience
- Uses System.Drawing.Common for image processing
- Uses Svg.NET for SVG icon rendering
- .NET 6.0 for modern C# features and performance
