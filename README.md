# PhotoViewer2

A modern image viewer application built with C# .NET 6 and Windows Forms.

## Features

- **Image Format Support**: PNG, JPG, JPEG, GIF, WebP, BMP
- **Alpha Channel Support**: Proper transparency display with checkered background
- **Navigation**: Previous/Next image browsing within folders
- **Zoom Controls**: Zoom in, zoom out, fit to window
- **Rotate Function**: Rotate images 90 degrees clockwise
- **Crop Functionality**: Select and crop image areas with mouse
- **Save Options**: Save images in various formats
- **Drag & Drop**: Drop image files to open them
- **Keyboard Shortcuts**: Arrow keys for navigation, +/- for zoom, R for rotate, Ctrl+O to open, Ctrl+S to save

## Requirements

- .NET 6.0 Runtime (Windows)
- Windows operating system

## Building

```bash
dotnet build
```

## Running

```bash
dotnet run
```

Or run the executable from `bin/Debug/net6.0-windows/PhotoViewer2.exe`

## File Associations

To set up file associations so you can double-click image files to open them with PhotoViewer2:

1. Run the provided batch file as administrator:
   ```cmd
   setup-file-associations.bat
   ```

2. Or manually set up associations:
   - Right-click on an image file
   - Select "Open with" → "Choose another app"
   - Browse to `bin/Debug/net6.0-windows/PhotoViewer2.exe`
   - Check "Always use this app to open .png files" (or the respective format)

## Usage

### Opening Images
- Click the "Open" button in the toolbar
- Drag and drop image files onto the application
- Use Ctrl+O keyboard shortcut
- Double-click image files (after setting up file associations)
- Command line: `PhotoViewer2.exe "path\to\image.jpg"`

### Navigation
- Use Previous/Next buttons in toolbar
- Use Left/Right arrow keys
- Automatically loads all images from the same folder

### Zoom Controls
- Zoom In/Out buttons in toolbar
- +/- keys on keyboard
- "Fit to Window" button to auto-fit image

### Rotate Feature
- Click the "Rotate" button in toolbar to rotate 90 degrees clockwise
- Use 'R' key for quick rotation
- Automatically adjusts zoom to fit rotated image

### Crop Feature
1. Click the "Crop" button to enter crop mode
2. Click and drag to select the area to crop
3. Confirm the crop operation
4. Press Escape to exit crop mode

### Saving
- Click "Save As" button in toolbar
- Use Ctrl+S keyboard shortcut
- Choose from PNG, JPEG, GIF, or BMP formats

## Icons

The application uses icons from Icons8. The following icons are included:
- Open folder icon
- Previous/Next navigation icons
- Zoom in/out icons
- Rotate icon
- Crop icon
- Save icon

## Architecture

- **MainForm.cs**: Main application window with all UI controls and event handlers
- **Program.cs**: Application entry point with command-line argument support
- **PhotoViewer.ico**: Application icon for executable and window
- Built with Windows Forms for native Windows experience
- Uses System.Drawing.Common for image processing
