# PhotoViewer2

A modern image viewer application built with C# .NET 6 and Windows Forms.

## Features

- **Image Format Support**: PNG, JPG, JPEG, GIF, WebP, BMP
- **Navigation**: Previous/Next image browsing within folders
- **Zoom Controls**: Zoom in, zoom out, fit to window
- **Crop Functionality**: Select and crop image areas with mouse
- **Save Options**: Save images in various formats
- **Drag & Drop**: Drop image files to open them
- **Keyboard Shortcuts**: Arrow keys for navigation, +/- for zoom, Ctrl+O to open, Ctrl+S to save

## Requirements

- .NET 6.0 Runtime (Windows)
- Windows operating system

## Building

```bash
dotnet build
```

## Running

```bash
dotnet run
```

Or run the executable from `bin/Debug/net6.0-windows/PhotoViewer2.exe`

## Usage

### Opening Images
- Click the "Open" button in the toolbar
- Drag and drop image files onto the application
- Use Ctrl+O keyboard shortcut

### Navigation
- Use Previous/Next buttons in toolbar
- Use Left/Right arrow keys
- Automatically loads all images from the same folder

### Zoom Controls
- Zoom In/Out buttons in toolbar
- +/- keys on keyboard
- "Fit to Window" button to auto-fit image

### Crop Feature
1. Click the "Crop" button to enter crop mode
2. Click and drag to select the area to crop
3. Confirm the crop operation
4. Press Escape to exit crop mode

### Saving
- Click "Save As" button in toolbar
- Use Ctrl+S keyboard shortcut
- Choose from PNG, JPEG, GIF, or BMP formats

## Icons

The application uses icons from Icons8. The following icons are included:
- Open folder icon
- Previous/Next navigation icons
- Zoom in/out icons
- Crop icon
- Save icon

## Architecture

- **MainForm.cs**: Main application window with all UI controls and event handlers
- **Program.cs**: Application entry point
- Built with Windows Forms for native Windows experience
- Uses System.Drawing.Common for image processing
