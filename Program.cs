using System;
using System.Windows.Forms;

namespace PhotoViewer2
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Check if a file was passed as command-line argument
            string? initialFile = null;
            if (args.Length > 0 && System.IO.File.Exists(args[0]))
            {
                initialFile = args[0];
            }

            Application.Run(new MainForm(initialFile));
        }
    }
}
