@echo off
echo Setting up PhotoViewer2 file associations...
echo.

REM Get the current directory where PhotoViewer2.exe is located
set "APP_PATH=%~dp0bin\Debug\net6.0-windows\PhotoViewer2.exe"

REM Check if the executable exists
if not exist "%APP_PATH%" (
    echo Error: PhotoViewer2.exe not found at %APP_PATH%
    echo Please build the application first using 'dotnet build'
    pause
    exit /b 1
)

echo Found PhotoViewer2.exe at: %APP_PATH%
echo.

REM Create registry entries for file associations
echo Creating file associations...

REM Register PhotoViewer2 as an application
reg add "HKEY_CURRENT_USER\Software\Classes\Applications\PhotoViewer2.exe" /f
reg add "HKEY_CURRENT_USER\Software\Classes\Applications\PhotoViewer2.exe\shell" /f
reg add "HKEY_CURRENT_USER\Software\Classes\Applications\PhotoViewer2.exe\shell\open" /f
reg add "HKEY_CURRENT_USER\Software\Classes\Applications\PhotoViewer2.exe\shell\open\command" /ve /t REG_SZ /d "\"%APP_PATH%\" \"%%1\"" /f

REM Associate image file types
echo Associating image file types...

REM PNG files
reg add "HKEY_CURRENT_USER\Software\Classes\.png\OpenWithList\PhotoViewer2.exe" /f

REM JPG files
reg add "HKEY_CURRENT_USER\Software\Classes\.jpg\OpenWithList\PhotoViewer2.exe" /f

REM JPEG files
reg add "HKEY_CURRENT_USER\Software\Classes\.jpeg\OpenWithList\PhotoViewer2.exe" /f

REM GIF files
reg add "HKEY_CURRENT_USER\Software\Classes\.gif\OpenWithList\PhotoViewer2.exe" /f

REM BMP files
reg add "HKEY_CURRENT_USER\Software\Classes\.bmp\OpenWithList\PhotoViewer2.exe" /f

REM WebP files
reg add "HKEY_CURRENT_USER\Software\Classes\.webp\OpenWithList\PhotoViewer2.exe" /f

echo.
echo File associations have been set up successfully!
echo You can now right-click on image files and select "Open with" -> "PhotoViewer2"
echo.
echo To make PhotoViewer2 the default image viewer:
echo 1. Right-click on an image file
echo 2. Select "Open with" -> "Choose another app"
echo 3. Select PhotoViewer2 and check "Always use this app"
echo.
pause
