﻿using System.Drawing;
using System.Reflection;
using Svg;

namespace PhotoViewer.Icons;

public static class IconProvider
{
    private static readonly Assembly _assembly = Assembly.GetExecutingAssembly();

    /// <summary>
    /// Gets an SVG icon as a bitmap with the specified size
    /// </summary>
    /// <param name="iconName">Name of the icon (without .svg extension)</param>
    /// <param name="size">Size of the resulting bitmap</param>
    /// <returns>Bitmap representation of the SVG icon, or null if not found</returns>
    public static Bitmap? GetIcon(string iconName, int size = 24)
    {
        return GetIcon(iconName, new Size(size, size));
    }

    /// <summary>
    /// Gets an SVG icon as a bitmap with the specified size
    /// </summary>
    /// <param name="iconName">Name of the icon (without .svg extension)</param>
    /// <param name="size">Size of the resulting bitmap</param>
    /// <returns>Bitmap representation of the SVG icon, or null if not found</returns>
    public static Bitmap? GetIcon(string iconName, Size size)
    {
        try
        {
            string resourceName = $"PhotoViewer.Icons.{iconName}.svg";

            using var stream = _assembly.GetManifestResourceStream(resourceName);
            if (stream == null)
            {
                System.Diagnostics.Debug.WriteLine($"Icon resource not found: {resourceName}");
                return null;
            }

            var svgDocument = SvgDocument.Open<SvgDocument>(stream);
            var bitmap = svgDocument.Draw(size.Width, size.Height);
            return bitmap;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error loading icon {iconName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Gets a list of all available icon names
    /// </summary>
    /// <returns>Array of icon names without .svg extension</returns>
    public static string[] GetAvailableIcons()
    {
        var resourceNames = _assembly.GetManifestResourceNames();
        return resourceNames
            .Where(name => name.StartsWith("PhotoViewer.Icons.") && name.EndsWith(".svg"))
            .Select(name => name.Substring("PhotoViewer.Icons.".Length, name.Length - "PhotoViewer.Icons.".Length - 4))
            .ToArray();
    }

    // Predefined icon names for easy access
    public static class Icons
    {
        public const string OpenFolder = "OpenFolder";
        public const string Previous = "Previous";
        public const string Next = "Next";
        public const string ZoomIn = "ZoomIn";
        public const string ZoomOut = "ZoomOut";
        public const string Rotate90Clockwise = "Rotate90Clockwise";
        public const string Crop = "Crop";
        public const string Save = "Save";
    }
}
