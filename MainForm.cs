using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace PhotoViewer2
{
    public partial class MainForm : Form
    {
        private PictureBox pictureBox = null!;
        private Panel imagePanel = null!;
        private ToolStrip toolStrip = null!;
        private StatusStrip statusStrip = null!;
        private ToolStripStatusLabel statusLabel = null!;

        private List<string>? imageFiles;
        private int currentImageIndex = -1;
        private float zoomFactor = 1.0f;
        private Image? originalImage;
        private string? currentDirectory;
        
        // Crop selection variables
        private bool isSelecting = false;
        private bool isCropMode = false;
        private Point selectionStart;
        private Point selectionEnd;
        private Rectangle selectionRect;
        
        // Supported image formats
        private readonly string[] supportedExtensions = { ".png", ".jpg", ".jpeg", ".gif", ".webp", ".bmp" };
        
        public MainForm(string? initialFile = null)
        {
            InitializeComponent();
            InitializeImageViewer();
            LoadIcons();

            // Load initial file if provided
            if (!string.IsNullOrEmpty(initialFile) && File.Exists(initialFile))
            {
                LoadImage(initialFile);
                LoadImagesFromDirectory(Path.GetDirectoryName(initialFile)!);
            }
        }
        
        private void InitializeImageViewer()
        {
            this.Text = "PhotoViewer2";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Set the application icon
            try
            {
                string iconPath = Path.Combine(Application.StartupPath, "PhotoViewer.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }
                else
                {
                    // Try relative path
                    iconPath = "PhotoViewer.ico";
                    if (File.Exists(iconPath))
                    {
                        this.Icon = new Icon(iconPath);
                    }
                }
            }
            catch (Exception ex)
            {
                // If icon loading fails, continue without icon
                System.Diagnostics.Debug.WriteLine($"Could not load icon: {ex.Message}");
            }
            
            // Create main panel for image with scrollbars
            imagePanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                BackColor = Color.LightGray
            };

            // Add paint event to draw transparency pattern
            imagePanel.Paint += ImagePanel_Paint;
            
            // Create picture box
            pictureBox = new PictureBox
            {
                SizeMode = PictureBoxSizeMode.Zoom,
                BackColor = Color.Transparent,
                Anchor = AnchorStyles.None
            };
            
            // Add mouse events for crop selection
            pictureBox.MouseDown += PictureBox_MouseDown;
            pictureBox.MouseMove += PictureBox_MouseMove;
            pictureBox.MouseUp += PictureBox_MouseUp;
            pictureBox.Paint += PictureBox_Paint;
            
            imagePanel.Controls.Add(pictureBox);
            
            // Create status strip
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("Ready");
            statusStrip.Items.Add(statusLabel);
            
            this.Controls.Add(imagePanel);
            this.Controls.Add(statusStrip);
            
            // Enable drag and drop
            this.AllowDrop = true;
            this.DragEnter += MainForm_DragEnter;
            this.DragDrop += MainForm_DragDrop;
        }
        
        private void LoadIcons()
        {
            try
            {
                string iconPath = Path.Combine(Application.StartupPath, "icons");
                if (!Directory.Exists(iconPath))
                {
                    iconPath = "icons"; // Try relative path
                }
                
                // Load icons if they exist
                var openIcon = LoadIcon(Path.Combine(iconPath, "icons8-opened-folder-100.png"));
                var prevIcon = LoadIcon(Path.Combine(iconPath, "icons8-prev-100.png"));
                var nextIcon = LoadIcon(Path.Combine(iconPath, "icons8-next-100.png"));
                var zoomInIcon = LoadIcon(Path.Combine(iconPath, "icons8-zoom-in-100.png"));
                var zoomOutIcon = LoadIcon(Path.Combine(iconPath, "icons8-zoom-out-100.png"));
                var rotateIcon = LoadIcon(Path.Combine(iconPath, "icons8-rotate-96.png"));
                var cropIcon = LoadIcon(Path.Combine(iconPath, "icons8-crop-100.png"));
                var saveIcon = LoadIcon(Path.Combine(iconPath, "icons8-save-as-100.png"));

                CreateToolStrip(openIcon, prevIcon, nextIcon, zoomInIcon, zoomOutIcon, rotateIcon, cropIcon, saveIcon);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading icons: {ex.Message}", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                CreateToolStrip(null, null, null, null, null, null, null, null);
            }
        }
        
        private Image? LoadIcon(string path)
        {
            if (File.Exists(path))
            {
                var icon = Image.FromFile(path);
                return new Bitmap(icon, new Size(24, 24));
            }
            return null;
        }
        
        private void CreateToolStrip(Image? openIcon, Image? prevIcon, Image? nextIcon,
            Image? zoomInIcon, Image? zoomOutIcon, Image? rotateIcon, Image? cropIcon, Image? saveIcon)
        {
            toolStrip = new ToolStrip
            {
                ImageScalingSize = new Size(24, 24)
            };
            
            // Open button
            var openButton = new ToolStripButton("Open", openIcon, OpenImage_Click);
            toolStrip.Items.Add(openButton);
            
            toolStrip.Items.Add(new ToolStripSeparator());
            
            // Navigation buttons
            var prevButton = new ToolStripButton("Previous", prevIcon, PreviousImage_Click);
            var nextButton = new ToolStripButton("Next", nextIcon, NextImage_Click);
            toolStrip.Items.Add(prevButton);
            toolStrip.Items.Add(nextButton);
            
            toolStrip.Items.Add(new ToolStripSeparator());
            
            // Zoom buttons
            var zoomInButton = new ToolStripButton("Zoom In", zoomInIcon, ZoomIn_Click);
            var zoomOutButton = new ToolStripButton("Zoom Out", zoomOutIcon, ZoomOut_Click);
            var fitButton = new ToolStripButton("Fit to Window", null, FitToWindow_Click);
            toolStrip.Items.Add(zoomInButton);
            toolStrip.Items.Add(zoomOutButton);
            toolStrip.Items.Add(fitButton);
            
            toolStrip.Items.Add(new ToolStripSeparator());

            // Rotate button
            var rotateButton = new ToolStripButton("Rotate", rotateIcon, RotateImage_Click);
            toolStrip.Items.Add(rotateButton);

            toolStrip.Items.Add(new ToolStripSeparator());

            // Crop button
            var cropButton = new ToolStripButton("Crop", cropIcon, ToggleCrop_Click);
            toolStrip.Items.Add(cropButton);

            toolStrip.Items.Add(new ToolStripSeparator());
            
            // Save button
            var saveButton = new ToolStripButton("Save As", saveIcon, SaveAs_Click);
            toolStrip.Items.Add(saveButton);
            
            this.Controls.Add(toolStrip);
        }

        #region Event Handlers

        private void OpenImage_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Image Files|*.png;*.jpg;*.jpeg;*.gif;*.webp;*.bmp|All Files|*.*";
                openFileDialog.Title = "Select an Image";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    LoadImage(openFileDialog.FileName);
                    LoadImagesFromDirectory(Path.GetDirectoryName(openFileDialog.FileName));
                }
            }
        }

        private void PreviousImage_Click(object sender, EventArgs e)
        {
            if (imageFiles != null && imageFiles.Count > 0 && currentImageIndex > 0)
            {
                currentImageIndex--;
                LoadImage(imageFiles[currentImageIndex]);
            }
        }

        private void NextImage_Click(object sender, EventArgs e)
        {
            if (imageFiles != null && imageFiles.Count > 0 && currentImageIndex < imageFiles.Count - 1)
            {
                currentImageIndex++;
                LoadImage(imageFiles[currentImageIndex]);
            }
        }

        private void ZoomIn_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                zoomFactor *= 1.25f;
                ApplyZoom();
            }
        }

        private void ZoomOut_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                zoomFactor /= 1.25f;
                if (zoomFactor < 0.1f) zoomFactor = 0.1f;
                ApplyZoom();
            }
        }

        private void FitToWindow_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                FitImageToWindow();
            }
        }

        private void RotateImage_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                RotateImage();
            }
        }

        private void ToggleCrop_Click(object sender, EventArgs e)
        {
            isCropMode = !isCropMode;
            pictureBox.Cursor = isCropMode ? Cursors.Cross : Cursors.Default;
            statusLabel.Text = isCropMode ? "Crop mode: Click and drag to select area" : "Ready";

            if (!isCropMode)
            {
                selectionRect = Rectangle.Empty;
                pictureBox.Invalidate();
            }
        }

        private void SaveAs_Click(object sender, EventArgs e)
        {
            if (pictureBox.Image == null) return;

            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "PNG Image|*.png|JPEG Image|*.jpg|GIF Image|*.gif|Bitmap|*.bmp";
                saveFileDialog.Title = "Save Image As";
                saveFileDialog.DefaultExt = "png";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        ImageFormat format = ImageFormat.Png;
                        string ext = Path.GetExtension(saveFileDialog.FileName).ToLower();

                        switch (ext)
                        {
                            case ".jpg":
                            case ".jpeg":
                                format = ImageFormat.Jpeg;
                                break;
                            case ".gif":
                                format = ImageFormat.Gif;
                                break;
                            case ".bmp":
                                format = ImageFormat.Bmp;
                                break;
                        }

                        pictureBox.Image.Save(saveFileDialog.FileName, format);
                        statusLabel.Text = $"Image saved: {Path.GetFileName(saveFileDialog.FileName)}";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error saving image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        #endregion

        #region Image Loading and Manipulation

        private void LoadImage(string filePath)
        {
            try
            {
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }

                // Load image with proper alpha channel support
                using (var tempImage = Image.FromFile(filePath))
                {
                    originalImage = new Bitmap(tempImage.Width, tempImage.Height, PixelFormat.Format32bppArgb);
                    using (var graphics = Graphics.FromImage(originalImage))
                    {
                        graphics.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceCopy;
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;

                        graphics.Clear(Color.Transparent);
                        graphics.DrawImage(tempImage, 0, 0);
                    }
                }

                pictureBox.Tag = filePath; // Store current file path
                zoomFactor = 1.0f;
                FitImageToWindow();

                statusLabel.Text = $"{Path.GetFileName(filePath)} - {originalImage.Width}x{originalImage.Height}";
                this.Text = $"PhotoViewer2 - {Path.GetFileName(filePath)}";

                // Reset crop mode
                isCropMode = false;
                selectionRect = Rectangle.Empty;
                pictureBox.Cursor = Cursors.Default;

                // Refresh the panel to redraw transparency pattern
                imagePanel.Invalidate();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadImagesFromDirectory(string directoryPath)
        {
            try
            {
                currentDirectory = directoryPath;
                imageFiles = Directory.GetFiles(directoryPath)
                    .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .OrderBy(file => file)
                    .ToList();

                if (imageFiles.Count > 0)
                {
                    string currentFile = pictureBox.Tag as string ?? "";
                    currentImageIndex = imageFiles.FindIndex(f => f.Equals(currentFile, StringComparison.OrdinalIgnoreCase));
                    if (currentImageIndex == -1) currentImageIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading directory: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FitImageToWindow()
        {
            if (originalImage == null) return;

            var panelSize = imagePanel.ClientSize;
            var imageSize = originalImage.Size;

            float scaleX = (float)panelSize.Width / imageSize.Width;
            float scaleY = (float)panelSize.Height / imageSize.Height;
            zoomFactor = Math.Min(scaleX, scaleY);

            if (zoomFactor > 1.0f) zoomFactor = 1.0f; // Don't scale up beyond original size

            ApplyZoom();
        }

        private void ApplyZoom()
        {
            if (originalImage == null) return;

            int newWidth = (int)(originalImage.Width * zoomFactor);
            int newHeight = (int)(originalImage.Height * zoomFactor);

            pictureBox.Size = new Size(newWidth, newHeight);

            // Create a new bitmap with proper alpha channel handling
            var scaledImage = CreateScaledImageWithTransparency(originalImage, newWidth, newHeight);
            pictureBox.Image?.Dispose(); // Dispose previous image
            pictureBox.Image = scaledImage;

            // Center the picture box in the panel
            pictureBox.Location = new Point(
                Math.Max(0, (imagePanel.ClientSize.Width - newWidth) / 2),
                Math.Max(0, (imagePanel.ClientSize.Height - newHeight) / 2)
            );

            statusLabel.Text = $"{Path.GetFileName(pictureBox.Tag as string ?? "")} - {originalImage.Width}x{originalImage.Height} - Zoom: {zoomFactor:P0}";
        }

        private Bitmap CreateScaledImageWithTransparency(Image sourceImage, int width, int height)
        {
            var scaledBitmap = new Bitmap(width, height, PixelFormat.Format32bppArgb);

            using (var graphics = Graphics.FromImage(scaledBitmap))
            {
                graphics.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceOver;
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;

                // Clear with transparent background
                graphics.Clear(Color.Transparent);

                // Draw the scaled image
                graphics.DrawImage(sourceImage, 0, 0, width, height);
            }

            return scaledBitmap;
        }

        private void ImagePanel_Paint(object sender, PaintEventArgs e)
        {
            // Draw transparency checkerboard pattern
            DrawTransparencyPattern(e.Graphics, imagePanel.ClientRectangle);
        }

        private void DrawTransparencyPattern(Graphics graphics, Rectangle bounds)
        {
            const int checkSize = 16;
            var lightBrush = new SolidBrush(Color.White);
            var darkBrush = new SolidBrush(Color.LightGray);

            try
            {
                for (int x = bounds.X; x < bounds.Right; x += checkSize)
                {
                    for (int y = bounds.Y; y < bounds.Bottom; y += checkSize)
                    {
                        var checkRect = new Rectangle(x, y,
                            Math.Min(checkSize, bounds.Right - x),
                            Math.Min(checkSize, bounds.Bottom - y));

                        // Alternate pattern
                        bool isLight = ((x / checkSize) + (y / checkSize)) % 2 == 0;
                        graphics.FillRectangle(isLight ? lightBrush : darkBrush, checkRect);
                    }
                }
            }
            finally
            {
                lightBrush.Dispose();
                darkBrush.Dispose();
            }
        }

        private void RotateImage()
        {
            if (originalImage == null) return;

            try
            {
                // Create a new bitmap with rotated dimensions and alpha support
                var rotatedImage = new Bitmap(originalImage.Height, originalImage.Width, PixelFormat.Format32bppArgb);

                using (var graphics = Graphics.FromImage(rotatedImage))
                {
                    // Set high quality rendering with transparency support
                    graphics.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceOver;
                    graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                    graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                    graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;

                    // Clear with transparent background
                    graphics.Clear(Color.Transparent);

                    // Translate to center and rotate 90 degrees clockwise
                    graphics.TranslateTransform(rotatedImage.Width / 2f, rotatedImage.Height / 2f);
                    graphics.RotateTransform(90);
                    graphics.TranslateTransform(-originalImage.Width / 2f, -originalImage.Height / 2f);

                    // Draw the original image
                    graphics.DrawImage(originalImage, 0, 0);
                }

                // Replace the original image
                originalImage.Dispose();
                originalImage = rotatedImage;

                // Reset zoom and fit to window
                zoomFactor = 1.0f;
                FitImageToWindow();

                statusLabel.Text = $"Image rotated - {originalImage.Width}x{originalImage.Height}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error rotating image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Crop Functionality

        private void PictureBox_MouseDown(object sender, MouseEventArgs e)
        {
            if (isCropMode && e.Button == MouseButtons.Left)
            {
                isSelecting = true;
                selectionStart = e.Location;
                selectionEnd = e.Location;
                selectionRect = Rectangle.Empty;
            }
        }

        private void PictureBox_MouseMove(object sender, MouseEventArgs e)
        {
            if (isCropMode && isSelecting)
            {
                selectionEnd = e.Location;

                int x = Math.Min(selectionStart.X, selectionEnd.X);
                int y = Math.Min(selectionStart.Y, selectionEnd.Y);
                int width = Math.Abs(selectionEnd.X - selectionStart.X);
                int height = Math.Abs(selectionEnd.Y - selectionStart.Y);

                selectionRect = new Rectangle(x, y, width, height);
                pictureBox.Invalidate();
            }
        }

        private void PictureBox_MouseUp(object sender, MouseEventArgs e)
        {
            if (isCropMode && isSelecting)
            {
                isSelecting = false;

                if (selectionRect.Width > 10 && selectionRect.Height > 10)
                {
                    var result = MessageBox.Show("Crop the selected area?", "Crop Image",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        CropImage();
                    }
                }
            }
        }

        private void PictureBox_Paint(object sender, PaintEventArgs e)
        {
            if (isCropMode && !selectionRect.IsEmpty)
            {
                using (var pen = new Pen(Color.Red, 2))
                {
                    pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
                    e.Graphics.DrawRectangle(pen, selectionRect);
                }

                // Draw semi-transparent overlay outside selection
                using (var brush = new SolidBrush(Color.FromArgb(100, Color.Black)))
                {
                    var pictureRect = pictureBox.ClientRectangle;

                    // Top
                    if (selectionRect.Y > 0)
                        e.Graphics.FillRectangle(brush, 0, 0, pictureRect.Width, selectionRect.Y);

                    // Bottom
                    if (selectionRect.Bottom < pictureRect.Height)
                        e.Graphics.FillRectangle(brush, 0, selectionRect.Bottom,
                            pictureRect.Width, pictureRect.Height - selectionRect.Bottom);

                    // Left
                    e.Graphics.FillRectangle(brush, 0, selectionRect.Y, selectionRect.X, selectionRect.Height);

                    // Right
                    if (selectionRect.Right < pictureRect.Width)
                        e.Graphics.FillRectangle(brush, selectionRect.Right, selectionRect.Y,
                            pictureRect.Width - selectionRect.Right, selectionRect.Height);
                }
            }
        }

        private void CropImage()
        {
            if (originalImage == null || selectionRect.IsEmpty) return;

            try
            {
                // Convert selection rectangle from display coordinates to image coordinates
                float scaleX = (float)originalImage.Width / pictureBox.Image.Width;
                float scaleY = (float)originalImage.Height / pictureBox.Image.Height;

                int cropX = (int)(selectionRect.X * scaleX);
                int cropY = (int)(selectionRect.Y * scaleY);
                int cropWidth = (int)(selectionRect.Width * scaleX);
                int cropHeight = (int)(selectionRect.Height * scaleY);

                // Ensure crop rectangle is within image bounds
                cropX = Math.Max(0, Math.Min(cropX, originalImage.Width - 1));
                cropY = Math.Max(0, Math.Min(cropY, originalImage.Height - 1));
                cropWidth = Math.Min(cropWidth, originalImage.Width - cropX);
                cropHeight = Math.Min(cropHeight, originalImage.Height - cropY);

                if (cropWidth > 0 && cropHeight > 0)
                {
                    var cropRect = new Rectangle(cropX, cropY, cropWidth, cropHeight);
                    var croppedImage = new Bitmap(cropWidth, cropHeight, PixelFormat.Format32bppArgb);

                    using (var graphics = Graphics.FromImage(croppedImage))
                    {
                        graphics.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceCopy;
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.Clear(Color.Transparent);
                        graphics.DrawImage(originalImage, new Rectangle(0, 0, cropWidth, cropHeight), cropRect, GraphicsUnit.Pixel);
                    }

                    originalImage.Dispose();
                    originalImage = croppedImage;

                    zoomFactor = 1.0f;
                    FitImageToWindow();

                    // Reset crop mode
                    isCropMode = false;
                    selectionRect = Rectangle.Empty;
                    pictureBox.Cursor = Cursors.Default;

                    statusLabel.Text = $"Image cropped to {cropWidth}x{cropHeight}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error cropping image: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Drag and Drop

        private void MainForm_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    e.Effect = DragDropEffects.Copy;
                }
                else
                {
                    e.Effect = DragDropEffects.None;
                }
            }
        }

        private void MainForm_DragDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Length > 0 && IsImageFile(files[0]))
                {
                    LoadImage(files[0]);
                    LoadImagesFromDirectory(Path.GetDirectoryName(files[0]));
                }
            }
        }

        private bool IsImageFile(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            return supportedExtensions.Contains(extension);
        }

        #endregion

        #region Keyboard Shortcuts

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            switch (keyData)
            {
                case Keys.Left:
                    PreviousImage_Click(null, null);
                    return true;
                case Keys.Right:
                    NextImage_Click(null, null);
                    return true;
                case Keys.Add:
                case Keys.Oemplus:
                    ZoomIn_Click(null, null);
                    return true;
                case Keys.Subtract:
                case Keys.OemMinus:
                    ZoomOut_Click(null, null);
                    return true;
                case Keys.Control | Keys.O:
                    OpenImage_Click(null, null);
                    return true;
                case Keys.Control | Keys.S:
                    SaveAs_Click(null, null);
                    return true;
                case Keys.R:
                    RotateImage_Click(null, null);
                    return true;
                case Keys.Escape:
                    if (isCropMode)
                    {
                        ToggleCrop_Click(null, null);
                        return true;
                    }
                    break;
            }

            return base.ProcessCmdKey(ref msg, keyData);
        }

        #endregion

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            originalImage?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
